<?php

use App\Models\User;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Route;
use App\Models\Subscription\Subscription;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Http\Request as HttpRequest;
use App\Http\Controllers\API\SyncController;
use App\Http\Controllers\API\GkashController;
use App\Http\Controllers\auth\LoginController;
use App\Http\Controllers\email\EmailController;
use App\Http\Controllers\API\v3\CAPayController;
use App\Http\Controllers\backend\HomeController;
use App\Http\Controllers\auth\RegisterController;
use App\Http\Controllers\backend\ReportController;
use App\Http\Controllers\backend\BarcodeController;
use App\Http\Controllers\backend\PaymentController;
use App\Http\Controllers\backend\ProductController;
use App\Http\Controllers\backend\ProfileController;
use App\Http\Controllers\Console\ConsoleController;
use App\Http\Controllers\backend\CustomerController;
use App\Http\Controllers\backend\EmployeeController;
use App\Http\Controllers\backend\MaterialController;
use App\Http\Controllers\Frontend\DefaultController;
use App\Http\Controllers\API\v3\FileUploadController;
use App\Http\Controllers\auth\InternalAuthController;
use App\Http\Controllers\InternalDashboardController;
use App\Http\Controllers\Payment\BayarCashController;
use App\Http\Controllers\Payment\BayarCashEMandateController;
use App\Http\Controllers\User\SubscriptionController;
use App\Http\Controllers\backend\CollectionController;
use App\Http\Controllers\backend\ExportFileController;
use App\Http\Controllers\auth\ForgotPasswordController;
use App\Http\Controllers\backend\ReportSalesController;
use App\Http\Controllers\backend\OrderHistoryController;
use App\Http\Controllers\Payment\AddonPaymentController;
use App\Http\Controllers\User\AddonManagementController;
use App\Http\Controllers\backend\Admin\FeatureController;
use App\Http\Controllers\backend\Admin\PosPlusController;
use App\Http\Controllers\backend\CompanyWizardController;
use App\Http\Controllers\auth\TrialRegistrationController;
use App\Http\Controllers\backend\ReportLogStockController;
use App\Http\Controllers\backend\ReportLowStockController;
use App\Http\Controllers\backend\ReportMoneyOutController;
use App\Http\Controllers\backend\ReportTopProductController;
use App\Http\Controllers\backend\Admin\LoggingViewController;
use App\Http\Controllers\Examples\BayarCashExampleController;
use App\Http\Controllers\backend\BizappRegistrationController;
use App\Http\Controllers\backend\ReportSalesByStaffController;
use App\Http\Controllers\auth\EventTrialRegistrationController;
use App\Http\Controllers\backend\Admin\ExceptionsLogController;
use App\Http\Controllers\Payment\SubscriptionPaymentController;
use App\Http\Controllers\User\SubscriptionManagementController;
use App\Http\Controllers\auth\SimpleTrialRegistrationController;
use App\Http\Controllers\backend\Admin\AdminDashboardController;
use App\Http\Controllers\backend\ReportSalesByPaymentController;
use App\Http\Controllers\backend\Admin\SubscriptionPlanController;
use App\Http\Controllers\API\v3\AuthController as V3AuthController;
use App\Http\Controllers\API\v2\OrderController as V2OrderController;

// Event Trial Registration Routes
Route::middleware('guest')->group(function () {
    Route::get("/event-trial-register-qr", [
        SimpleTrialRegistrationController::class,
        "showRegisterForm",
    ])->name("simple.event.trial.register.form");
    Route::post("/event-trial-register-qr", [
        SimpleTrialRegistrationController::class,
        "register",
    ])->name("simple.event.trial.register");
});

Route::middleware("auth")->group(function () {
    Route::get("/event-trial-register-qr/onboarding", [
        SimpleTrialRegistrationController::class,
        "showOnboardingForm",
    ])->name("event.trial.onboarding.form");
    Route::post("/event-trial-register-qr/onboarding", [
        SimpleTrialRegistrationController::class,
        "completeOnboarding",
    ])->name("event.trial.onboarding");
    Route::get("/dashboard", function () {
        return view("dashboard");
    })->name("dashboard");
});


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something superb!
|
*/

Route::get("/", function () {
    if (Auth::check()) {
        return redirect()->route("dashboard");
    } else {
        return redirect()->route("login");
    }
});

Route::get("/test-redis", function () {
    try {
        $redis = Redis::connection();
        $redis->set("test_key", "test_value");
        $value = $redis->get("test_key");
        return "Redis is working! Value: " . $value;
    } catch (\Exception $e) {
        return "Could not connect to Redis: " . $e->getMessage();
    }
});

// pusher testing
Route::get("pusher-test", function () {
    return view("frontend.pusher-test");
});

Route::get("/test-pusher", function () {
    Broadcast::routes();
    return "Pusher is connected!";
});

Route::get("/broadcast-test", function () {
    event(new \App\Events\DashboardUpdated(["message" => "Test broadcast"]));
    return "Broadcast sent!";
});

// bulk register to bizapp
Route::get("/register-companies", [
    BizappRegistrationController::class,
    "registerCompanies",
]);

// payments page
Route::get("/payment-fail", function () {
    return view("frontend.payment.payment-fail");
})->name("payment.fail");
Route::get("/payment-success", function () {
    return view("frontend.payment.payment-success");
})->name("payment.success");

// redirect old MBI url to another subdomain
Route::get("/mbi", function () {
    return redirect()->to("https://mbi.bizappos.com")->send();
});
Route::get("/mbi/login", function () {
    return redirect()->to("https://mbi.bizappos.com")->send();
});

// Get plats duplicated data
Route::get("getDupePlats", [SyncController::class, "getPlatsDuplicatedData"]);
// Update plats displaced coupon column
Route::get("updatePlatsCouponRows", [
    SyncController::class,
    "updatePlatsCouponRows",
]);
// find matched gkash record for UAV
Route::get("crossCheckGkashUav", [SyncController::class, "crossCheckGkashUav"]);
// find all uav merchants order data
Route::get("getTotalGkashOrderForUav", [
    SyncController::class,
    "getTotalGkashOrderForUav",
]);
// get all matching records for uav users
Route::get("mathingUav", [SyncController::class, "mathingUav"]);
// remove gkash orders that does not appear in gkashes table
Route::get("removeDupeOnGkahesTable", [
    SyncController::class,
    "removeDupeOnGkahesTable",
]);
// remove fruitaddicts extra order
Route::get("removeBulkOrderFromBizappos", [
    V2OrderController::class,
    "removeBulkOrderFromBizappos",
]);

///  report sync

Route::get("reportSync", [SyncController::class, "reportSync"]);
Route::get("dupeOrders", [SyncController::class, "getDupeOrders"]);
Route::get("productSync", [SyncController::class, "productSync"]);
Route::get("employeeSync", [SyncController::class, "employeeSync"]);
Route::get("orderSync", [SyncController::class, "orderSync"]);
Route::get("customerSync", [SyncController::class, "customerSyncAll"]);

// migrate to v2
Route::get("migratev2", [SyncController::class, "migratev2"]);

// convert varchar datatype to decimal
Route::get("fill-decimals", [HomeController::class, "fillDecimals"]);
Route::get("fill-decimals-details", [
    HomeController::class,
    "fillDecimalsInDetails",
]);

// make sure all users have either company or employee relation
Route::get("fillCompanyRelations", [
    HomeController::class,
    "fillCompanyRelations",
]);
// deleteProductsWithReferralTable
Route::get("deleteFalseCompanies", [
    HomeController::class,
    "deleteProductsWithReferralTable",
]);
// fill company ids to order table
Route::get("fillCompanyIds", [HomeController::class, "fillCompanyIdOnOrders"]);
// restore users with missing user details / companies / employee data

Route::get("restoreUserDetails", [HomeController::class, "restoreUserDetails"]);
// sync order by pid
Route::get("ordersyncbypid", [SyncController::class, "orderSyncByPid"]);
// sync order by date
Route::get("ordersyncbydate", [SyncController::class, "orderSyncByDate"]);
// sync all HQs collections
Route::get("sync-all-collections", [
    CollectionController::class,
    "syncCollectionAllUsers",
]);
// sync user expire date
Route::get("sync-user-expire-date", [
    SyncController::class,
    "syncUserExpireDate",
]);
///
// upgrade form
Route::get("upgrade-form-modal", function () {
    return view("frontend.login-modal");
})->name("login.modal");
Route::post("upgrade-form-modal-process", [
    DefaultController::class,
    "loginCheckModal",
])->name("upgrade.form.modal.process");
Route::view("upgrade-form", "frontend.upgrade-form")->name("upgrade.form");
Route::post("upgrade-form-process", [
    DefaultController::class,
    "upgradeForm",
])->name("upgrade.form.process");
Route::get("payment-error", function () {
    return view("error.payment-error");
});

Route::get("get-user-signature", function () {
    return view("frontend.signature-full");
});
Route::post("process-signature", [
    DefaultController::class,
    "processSignature",
])->name("signature.pad.process");

Route::get("testCalculate/{id}", [
    OrderHistoryController::class,
    "getAllOrderPaymentMethod",
])->name("test.calculate");

//errors
Route::get("403", function () {
    return view("error.error-403");
});

Route::get("privacy-policy", [DefaultController::class, "privacyPolicy"])->name(
    "privacy"
);

Route::get("test", [GkashController::class, "testGkashPost"])->name("test");
Route::get("Webreturn", [GkashController::class, "testWebreturn"])->name(
    "payment.done"
);

// Debug routes to test dd() function
Route::get("debug-dd", function () {
    dd("Debug test - if you see this, dd() is working properly");
});

// Debug route to test POST requests with dd()
Route::post("debug-post", function (HttpRequest $request) {
    dd($request->all());
});

// Bizappay test route
Route::get("test-bizappay", [
    \App\Http\Controllers\API\BizappayTestController::class,
    "testConnection",
])->name("test.bizappay");

// BayarCash example routes
Route::prefix("examples/bayarcash")->group(function () {
    Route::get("/example1", [
        BayarCashExampleController::class,
        "example1",
    ])->name("examples.bayarcash.example1");
    Route::get("/example2", [
        BayarCashExampleController::class,
        "example2",
    ])->name("examples.bayarcash.example2");
    Route::get("/example3", [
        BayarCashExampleController::class,
        "example3",
    ])->name("examples.bayarcash.example3");
    Route::post("/example4", [
        BayarCashExampleController::class,
        "example4",
    ])->name("examples.bayarcash.example4");
    Route::post("/example5", [
        BayarCashExampleController::class,
        "example5",
    ])->name("examples.bayarcash.example5");
    Route::post("/example6", [
        BayarCashExampleController::class,
        "example6",
    ])->name("examples.bayarcash.example6");
    Route::get("/example7", [
        BayarCashExampleController::class,
        "example7",
    ])->name("examples.bayarcash.example7");
    Route::get("/example8", [
        BayarCashExampleController::class,
        "example8",
    ])->name("examples.bayarcash.example8");
    Route::get("/example9", [
        BayarCashExampleController::class,
        "example9",
    ])->name("examples.bayarcash.example9");
    Route::get("/example10", [
        BayarCashExampleController::class,
        "example10",
    ])->name("examples.bayarcash.example10");
});

// caPay
Route::get("cap-webreturn", [CAPayController::class, "webreturn"])->name(
    "cap.payment.done"
);
Route::post("cap-callback", [CAPayController::class, "capCallback"])->name(
    "cap.callback"
);

// Authentication routes (login, register, forgot password) - only for guests
Route::middleware('guest')->group(function () {
    Route::get("login", [LoginController::class, "index"])->name("login");
    Route::post("login-process", [LoginController::class, "processLoginV2"])->name(
        "login.process"
    );
    Route::get("login-admin", [LoginController::class, "indexAdmin"])->name(
        "login.admin"
    );
    Route::post("login-process-admin", [
        LoginController::class,
        "processLoginAdmin",
    ])->name("login.process.admin");
    //
    Route::get("mobileBackoffice/{uname}/{domain}", [
        LoginController::class,
        "mobileBackoffice",
    ])->name("login.mobilebackoffice");

    Route::get("forgot-password", [
        ForgotPasswordController::class,
        "forgotPassword",
    ])->name("login.forgot");
    Route::post("forgot-password-process", [
        ForgotPasswordController::class,
        "forgotPasswordProcess",
    ])->name("login.forgot.process");
    Route::get("reset-password/{token}", [
        ForgotPasswordController::class,
        "showResetPasswordForm",
    ])->name("reset.password.get");
    Route::post("reset-password", [
        ForgotPasswordController::class,
        "submitResetPasswordForm",
    ])->name("reset.password.post");

    Route::get("register-prime-trial", [
        RegisterController::class,
        "primeTrial",
    ])->name("register.prime.trial");
    Route::post("register-prime-trial-process", [
        RegisterController::class,
        "processPrimeTrial",
    ])->name("register.prime.trial.process");

    // Trial Registration Routes
    Route::get("register-trial", [
        TrialRegistrationController::class,
        "index",
    ])->name("trial.register");
    Route::post("register-trial-process", [
        TrialRegistrationController::class,
        "processTrialRegistration",
    ])->name("trial.register.process");
    Route::post("register-trial-validate-step", [
        TrialRegistrationController::class,
        "validateStep",
    ])->name("trial.register.validate.step");
    Route::post("register-trial-check-username", [
        TrialRegistrationController::class,
        "checkUsername",
    ])->name("trial.register.check.username");

    // Event Trial Registration Routes
    Route::get("event-trial-register", [
        EventTrialRegistrationController::class,
        "index",
    ])->name("event.trial.register");
    Route::post("event-trial-register", [
        EventTrialRegistrationController::class,
        "processTrialRegistration",
    ])->name("event.trial.register.process");
    Route::post("event-trial-register/validate", [
        EventTrialRegistrationController::class,
        "validateStep",
    ])->name("event.trial.register.validate");
    Route::post("event-trial-register/check-username", [
        EventTrialRegistrationController::class,
        "checkUsername",
    ])->name("event.trial.register.check-username");

    Route::get("register", [RegisterController::class, "index"])->name("register");
    Route::post("register-process", [
        RegisterController::class,
        "processRegister",
    ])->name("register.process");
    Route::post("check-username", [
        RegisterController::class,
        "checkUsername",
    ])->name("register.check.username");
});

// Subscription payment routes
Route::get("payment/subscription/{subscription}", [
    SubscriptionPaymentController::class,
    "processPayment",
])->name("payment.subscription");
Route::get("payment/subscription/return", [
    SubscriptionPaymentController::class,
    "handleReturn",
])->name("payment.subscription.return");
Route::post("payment/subscription/callback", [
    SubscriptionPaymentController::class,
    "handleCallback",
])->name("payment.subscription.callback");

// Addon payment routes
Route::get("payment/addon/{userFeature}", [
    AddonPaymentController::class,
    "processPayment",
])->name("payment.addon");
Route::get("payment/addon/return", [
    AddonPaymentController::class,
    "handleReturn",
])->name("payment.addon.return");
Route::post("payment/addon/callback", [
    AddonPaymentController::class,
    "handleCallback",
])->name("payment.addon.callback");

// BayarCash payment routes
Route::prefix("payment/bayarcash")->group(function () {
    Route::get("/portals", [BayarCashController::class, "getPortals"])->name(
        "payment.bayarcash.portals"
    );
    Route::get("/channels", [BayarCashController::class, "getChannels"])->name(
        "payment.bayarcash.channels"
    );
    Route::post("/create", [
        BayarCashController::class,
        "createPaymentIntent",
    ])->name("payment.bayarcash.create");
    Route::post("/pre-transaction-callback", [
        BayarCashController::class,
        "handlePreTransactionCallback",
    ])->name("payment.bayarcash.pre-callback");
    Route::post("/callback", [
        BayarCashController::class,
        "handleTransactionCallback",
    ])->name("payment.bayarcash.callback");
    Route::get("/return", [
        BayarCashController::class,
        "handleReturnUrlCallback",
    ])->name("payment.bayarcash.return");
    Route::get("/transaction", [
        BayarCashController::class,
        "getTransactionDetails",
    ])->name("payment.bayarcash.transaction");
    Route::get("/transactions", [
        BayarCashController::class,
        "getAllTransactions",
    ])->name("payment.bayarcash.transactions");

    // BayarCash registration payment routes
    Route::post("/registration/callback", [
        RegisterController::class,
        "handleBayarCashCallback",
    ])->name("payment.bayarcash.registration.callback");
    Route::get("/registration/return", [
        RegisterController::class,
        "handleBayarCashReturn",
    ])->name("payment.bayarcash.registration.return");
});

// BayarCash E-Mandate/FPX Direct Debit payment routes
Route::prefix("payment/bayarcash-emandate")->group(function () {
    // E-mandate enrollment routes
    Route::get("/enrollment", [
        BayarCashEMandateController::class,
        "showEnrollmentForm",
    ])->name("payment.bayarcash-emandate.enrollment");

    Route::post("/create", [
        BayarCashEMandateController::class,
        "createEnrollmentIntent",
    ])->name("payment.bayarcash-emandate.create");

    // Callback and return URL routes
    Route::post("/callback", [
        BayarCashEMandateController::class,
        "handleEnrollmentCallback",
    ])->name("payment.bayarcash-emandate.callback");

    Route::get("/success", [
        BayarCashEMandateController::class,
        "handleSuccessReturn",
    ])->name("payment.bayarcash-emandate.success");

    Route::get("/failed", [
        BayarCashEMandateController::class,
        "handleFailedReturn",
    ])->name("payment.bayarcash-emandate.failed");

    // Status checking routes
    Route::get("/status-checker", [
        BayarCashEMandateController::class,
        "showStatusChecker",
    ])->name("payment.bayarcash-emandate.status-checker");

    Route::post("/check-status", [
        BayarCashEMandateController::class,
        "checkEnrollmentStatus",
    ])->name("payment.bayarcash-emandate.check-status");

    // User enrollment management
    Route::get("/my-enrollments", [
        BayarCashEMandateController::class,
        "getUserEnrollments",
    ])->name("payment.bayarcash-emandate.my-enrollments");
});

// BayarCash E-Mandate Test Routes (for development/testing)
Route::prefix("test/bayarcash-emandate")->group(function () {
    Route::get("/", function () {
        return view('frontend.payment.bayarcash-emandate-test');
    })->name("test.bayarcash-emandate.index");
});

Route::get("register-bulk", function () {
    return view("frontend.bulk.register-bulk-upload");
});

Route::get("logout", [LoginController::class, "logout"])->name("logout");

// one-time queues
Route::get("copy-company-to-receipts", [
    HomeController::class,
    "copyCompanyToReceiptQueueJob",
]);

// MAIL
Route::get("/send-email", [EmailController::class, "index"]);

Route::middleware(["admin"])->group(function () {
    // --------------------------------------------- ADMIN AREA ------------------------------------------------------
    Route::get("admin-dashboard", [
        AdminDashboardController::class,
        "index",
    ])->name("admin.dashboard");

    Route::get("posplus", [PosPlusController::class, "index"])->name("posplus");
    Route::get("posplus-detail/{detail}", [
        PosPlusController::class,
        "getDetail",
    ])->name("posplus.detail");
    Route::get("posplus-pdf/{id}", [PosPlusController::class, "getPdf"])->name(
        "posplus.pdf"
    );

    Route::get("logging", [LoggingViewController::class, "index"])->name(
        "logging"
    );
    Route::post("loggingNewDate", [
        LoggingViewController::class,
        "changeDate",
    ])->name("logging.newdate");

    Route::prefix("exceptions")->group(function () {
        Route::get("/", [ExceptionsLogController::class, "index"])->name(
            "exceptions.index"
        );
        Route::get("/{exception}", [
            ExceptionsLogController::class,
            "show",
        ])->name("exceptions.show");
        Route::put("/{exception}", [
            ExceptionsLogController::class,
            "update",
        ])->name("exceptions.update");
    });

    // Subscription and Feature Management Routes
    Route::prefix("features")->group(function () {
        Route::get("/", [FeatureController::class, "index"])->name(
            "admin.features.index"
        );
        Route::get("/create", [FeatureController::class, "create"])->name(
            "admin.features.create"
        );
        Route::post("/", [FeatureController::class, "store"])->name(
            "admin.features.store"
        );
        Route::get("/{id}", [FeatureController::class, "show"])->name(
            "admin.features.show"
        );
        Route::get("/{id}/edit", [FeatureController::class, "edit"])->name(
            "admin.features.edit"
        );
        Route::put("/{id}", [FeatureController::class, "update"])->name(
            "admin.features.update"
        );
        Route::delete("/{id}", [FeatureController::class, "destroy"])->name(
            "admin.features.destroy"
        );
    });

    Route::prefix("subscription-plans")->group(function () {
        Route::get("/", [SubscriptionPlanController::class, "index"])->name(
            "admin.subscription-plans.index"
        );
        Route::get("/create", [
            SubscriptionPlanController::class,
            "create",
        ])->name("admin.subscription-plans.create");
        Route::post("/", [SubscriptionPlanController::class, "store"])->name(
            "admin.subscription-plans.store"
        );
        Route::get("/{id}", [SubscriptionPlanController::class, "show"])->name(
            "admin.subscription-plans.show"
        );
        Route::get("/{id}/edit", [
            SubscriptionPlanController::class,
            "edit",
        ])->name("admin.subscription-plans.edit");
        Route::put("/{id}", [
            SubscriptionPlanController::class,
            "update",
        ])->name("admin.subscription-plans.update");
        Route::delete("/{id}", [
            SubscriptionPlanController::class,
            "destroy",
        ])->name("admin.subscription-plans.destroy");
    });
});

Route::middleware(["auth"])->group(function () {
    // Company wizard routes
    Route::prefix("company")->group(function () {
        Route::get("wizard", [CompanyWizardController::class, "show"])->name(
            "company.wizard"
        );
        Route::post("wizard", [CompanyWizardController::class, "store"])->name(
            "company.wizard.store"
        );
    });

    // --------------------------------------------- USER AREA ------------------------------------------------------
    Route::get("dashboard", [HomeController::class, "index"])
        ->middleware("company.complete")
        ->name("dashboard");

    Route::get("mastermerchant-dashboard", [
        HomeController::class,
        "masterMerchantIndex",
    ])->name("mastermerchant.dashboard");
    Route::get("mm-profile", [ProfileController::class, "mmIndex"])->name(
        "mm.profile"
    );
    Route::get("mm-reports", [ReportController::class, "mmIndex"])->name(
        "mm.reports"
    );
    Route::post("download-pay-report", [
        ReportController::class,
        "mmPayMethodReport",
    ])->name("mm.download.report1");

    // Route::post('sync-bizapp',[ProfileController::class,'syncWithBizapp'])->name('bizapp.sync'); // TODO: make this API to sync all user data from BIZAPP

    Route::get("profile", [ProfileController::class, "index"])->name("profile");
    Route::post("update-business", [
        ProfileController::class,
        "updateBusiness",
    ])->name("profile.business.update");
    Route::post("update-profile", [
        ProfileController::class,
        "updateProfile",
    ])->name("profile.update");
    Route::post("update-profile-picture", [
        ProfileController::class,
        "updateProfilePic",
    ])->name("profile.pic.update");
    Route::post("update-profile-password", [
        ProfileController::class,
        "updatePassword",
    ])->name("profile.password.update");
    Route::post("update-profile-receipt", [
        ProfileController::class,
        "updateReceiptDisplay",
    ])->name("profile.receipt.update");
    Route::post("remove-profile-receipt-logo", [
        ProfileController::class,
        "removeReceiptLogo",
    ])->name("profile.receipt.remove-logo");

    Route::prefix("order-history")->group(function () {
        Route::get("/", [OrderHistoryController::class, "index"])->name(
            "order.history"
        );
        Route::get("/data", [OrderHistoryController::class, "data"])->name(
            "order.history.data"
        );
        Route::get("/view/{id}", [OrderHistoryController::class, "view"])->name(
            "order.history.view"
        );
        Route::get("/order/{id}/download-pdf", [
            OrderHistoryController::class,
            "downloadPDF",
        ])->name("order.download.pdf");
    });

    // Route::get('order-history',[OrderHistoryController::class,'index'])->name('order.history');
    Route::get("order-history-detail/{detailId}", [
        OrderHistoryController::class,
        "indexDetail",
    ])->name("order.history.detail");
    Route::post("order-history-create", [
        OrderHistoryController::class,
        "create",
    ])->name("order.history.create");
    Route::match(["get", "post"], "order-history-delete/{id}", [
        OrderHistoryController::class,
        "deleteOrderHistory",
    ])->name("order.history.delete");
    // Route::get('order-history-view/{id}',[OrderHistoryController::class,'viewDetail'])->name('order.history.view');

    Route::prefix("customer")->group(function () {
        Route::get("/", [CustomerController::class, "index"])->name(
            "customers"
        );
        Route::get("/data", [CustomerController::class, "data"])->name(
            "customer.data"
        );
        Route::get("/create", [CustomerController::class, "create"])->name(
            "customer.create"
        );
        Route::post("/create", [CustomerController::class, "store"])->name(
            "customer.store"
        );
        Route::get("/edit/{id}", [CustomerController::class, "edit"])->name(
            "customer.edit"
        );
        Route::post("/edit/{id}", [CustomerController::class, "update"])->name(
            "customer.update"
        );
        Route::post("/delete", [CustomerController::class, "destroy"])->name(
            "customer.destroy"
        );
    });

    // Route::get('customers-detail/{detailId}',[CustomerController::class,'indexDetail'])->name('customer.detail');
    // Route::get('customer-create',[CustomerController::class,'create'])->name('customer.create');
    Route::post("customer-create-process", [
        CustomerController::class,
        "customerCreateProcess",
    ])->name("customer.create.process");

    Route::prefix("product")->group(function () {
        Route::get("/", [ProductController::class, "index"])->name("products");
        Route::get("/data", [ProductController::class, "data"])->name(
            "product.data"
        );
        Route::get("/products-data", [
            ProductController::class,
            "getProductsData",
        ])->name("products.data");
        Route::get("/sync", [ProductController::class, "syncProduct"])->name(
            "product.sync"
        );
        Route::post("/check-sku", [ProductController::class, "checkSKU"])->name(
            "product.check.sku"
        );
        Route::get("/add", [ProductController::class, "create"])->name(
            "product.create"
        );
        Route::post("/add-v2", [ProductController::class, "storeV2"])->name(
            "product.store.v2"
        );
        Route::post("/store", [ProductController::class, "store"])->name(
            "product.store"
        );
        Route::get("/edit/{productId}", [
            ProductController::class,
            "productEdit",
        ])->name("product.edit");
        Route::post("/update/{productId}", [
            ProductController::class,
            "productUpdate",
        ])->name("product.update");
        Route::delete("/delete/{productId}", [
            ProductController::class,
            "productDelete",
        ])->name("product.delete");
        Route::get("/productList", [
            ProductController::class,
            "productList",
        ])->name("product.list");

        // Barcode routes
        Route::post("/generate-barcodes", [
            BarcodeController::class,
            "generateBarcodes",
        ])->name("product.generate.barcodes");
        Route::get("/barcode-image/{sku}", [
            BarcodeController::class,
            "generateBarcodeImage",
        ])->name("product.barcode.image");
    });

    Route::prefix("collection")->group(function () {
        Route::get("/", [CollectionController::class, "index"])->name(
            "collections"
        );
        Route::get("/data", [CollectionController::class, "data"])->name(
            "collection.data"
        );
        Route::get("/list", [
            CollectionController::class,
            "getCollectionsList",
        ])->name("collections.list");
        Route::get("/sync", [
            CollectionController::class,
            "syncCollection",
        ])->name("collection.sync");
        Route::get("/collection/add", [
            CollectionController::class,
            "create",
        ])->name("collection.create");
        Route::post("/store", [CollectionController::class, "store"])->name(
            "collection.store"
        );
        Route::get("/edit/{colId}", [
            CollectionController::class,
            "collectionEdit",
        ])->name("collection.edit");
        Route::post("/update/{colId}", [
            CollectionController::class,
            "collectionUpdate",
        ])->name("collection.update");
        Route::post("/delete/{colId}", [
            CollectionController::class,
            "collectionDelete",
        ])->name("collection.delete");
        Route::post("/collection-add-product", [
            CollectionController::class,
            "collectionAddProduct",
        ])->name("collection.add.product");
        Route::post("/collection-delete-product/{id}", [
            CollectionController::class,
            "deleteCollectionProduct",
        ])->name("collection.delete-product");
        Route::post("collection/toggle/{id}", [
            CollectionController::class,
            "toggle",
        ])->name("collection.toggle");
    });

    Route::prefix("materials")->group(function () {
        Route::get("/", [MaterialController::class, "index"])->name(
            "materials"
        );
        Route::get("/data", [MaterialController::class, "data"])->name(
            "material.data"
        );
        Route::post("/add", [MaterialController::class, "store"])->name(
            "material.store"
        );
        Route::post("/update-stock", [
            MaterialController::class,
            "updateStock",
        ])->name("material.update.stock");
        Route::post("/delete", [MaterialController::class, "destroy"])->name(
            "material.destroy"
        );
    });

    // Route::get('products',[ProductController::class,'index'])->name('products');
    // Route::get('product-edit/{productId}',[ProductController::class,'productEdit'])->name('product.edit');
    // Route::post('product-create',[ProductController::class,'createProduct'])->name('product.create');
    // Route::get('delete-product/{id}',[ProductController::class,'deleteProduct'])->name('product.delete');

    Route::prefix("employee")->group(function () {
        Route::get("/", [EmployeeController::class, "index"])->name(
            "employees"
        );
        Route::get("/data", [EmployeeController::class, "data"])->name(
            "employee.data"
        );
        Route::get("/sync", [EmployeeController::class, "sync"])->name(
            "employee.sync"
        );
    });

    // Route::get('employees',[EmployeeController::class,'index'])->name('employees');
    Route::get("employee-detail/{detailId}", [
        EmployeeController::class,
        "indexDetail",
    ])->name("employee.detail");
    Route::post("employee-create", [EmployeeController::class, "create"])->name(
        "employee.create"
    );

    Route::prefix("export")->group(function () {
        Route::get("/", [ExportFileController::class, "index"])->name("export");
        Route::get("/data", [ExportFileController::class, "data"])->name(
            "export.data"
        );
        Route::post("/delete", [ExportFileController::class, "destroy"])->name(
            "export.destroy"
        );
        Route::get("download/{hash}", [
            ExportFileController::class,
            "download",
        ])->name("export.download");
    });

    Route::prefix("report/low-stock")->group(function () {
        Route::get("/", [ReportLowStockController::class, "index"])->name(
            "report.low_stock"
        );
        Route::get("/data", [ReportLowStockController::class, "data"])->name(
            "report.low_stock.data"
        );
        Route::post("/export", [
            ReportLowStockController::class,
            "export",
        ])->name("report.low_stock.export");
    });

    Route::prefix("report/log-stock")->group(function () {
        Route::get("/", [ReportLogStockController::class, "index"])->name(
            "report.log_stock"
        );
        Route::get("/data", [ReportLogStockController::class, "data"])->name(
            "report.log_stock.data"
        );
        Route::post("/export", [
            ReportLogStockController::class,
            "export",
        ])->name("report.log_stock.export");
    });

    Route::prefix("report/sales")->group(function () {
        Route::get("/", [ReportSalesController::class, "index"])->name(
            "report.sales"
        );
        Route::get("/data", [ReportSalesController::class, "data"])->name(
            "report.sales.data"
        );
        Route::post("/export", [ReportSalesController::class, "export"])->name(
            "report.sales.export"
        );
    });

    Route::prefix("report/top-product")->group(function () {
        Route::get("/", [ReportTopProductController::class, "index"])->name(
            "report.top_product"
        );
        Route::get("/data", [ReportTopProductController::class, "data"])->name(
            "report.top_product.data"
        );
        Route::post("/export", [
            ReportTopProductController::class,
            "export",
        ])->name("report.top_product.export");
    });

    Route::prefix("report/sales-by-payment")->group(function () {
        Route::get("/", [ReportSalesByPaymentController::class, "index"])->name(
            "report.sales_payment"
        );
        Route::get("/data", [
            ReportSalesByPaymentController::class,
            "data",
        ])->name("report.sales_payment.data");
        Route::post("/export", [
            ReportSalesByPaymentController::class,
            "export",
        ])->name("report.sales_payment.export");
    });

    Route::prefix("report/sales-by-staff")->group(function () {
        Route::get("/", [ReportSalesByStaffController::class, "index"])->name(
            "report.sales_staff"
        );
        Route::get("/data", [
            ReportSalesByStaffController::class,
            "data",
        ])->name("report.sales_staff.data");
        Route::post("/export", [
            ReportSalesByStaffController::class,
            "export",
        ])->name("report.sales_staff.export");
    });

    Route::prefix("report/money-out")->group(function () {
        Route::get("/", [ReportMoneyOutController::class, "index"])->name(
            "report.money_out"
        );
        Route::get("/create", [
            ReportMoneyOutController::class,
            "create",
        ])->name("report.money_out.create");
        Route::post("/", [ReportMoneyOutController::class, "store"])->name(
            "report.money_out.store"
        );
        Route::get("/{id}/get", [
            ReportMoneyOutController::class,
            "getRecord",
        ])->name("report.money_out.show");
        Route::get("/{id}/receipt", [
            ReportMoneyOutController::class,
            "serveReceipt",
        ])->name("report.money_out.receipt");
        Route::put("/{id}", [ReportMoneyOutController::class, "update"])->name(
            "report.money_out.update"
        );
        Route::delete("/{id}", [
            ReportMoneyOutController::class,
            "destroy",
        ])->name("report.money_out.destroy");

        // Category routes
        Route::get("/category", [
            ReportMoneyOutController::class,
            "categoryIndex",
        ])->name("report.money_out.category");
        Route::post("/category", [
            ReportMoneyOutController::class,
            "categoryStore",
        ])->name("report.money_out.category.store");
        Route::put("/category/{id}", [
            ReportMoneyOutController::class,
            "categoryUpdate",
        ])->name("report.money_out.category.update");
        Route::delete("/category/{id}", [
            ReportMoneyOutController::class,
            "categoryDestroy",
        ])->name("report.money_out.category.destroy");

        // Subcategory routes
        Route::get("/subcategory", [
            ReportMoneyOutController::class,
            "subcategoryIndex",
        ])->name("report.money_out.subcategory.index");
        Route::post("/subcategory", [
            ReportMoneyOutController::class,
            "subcategoryStore",
        ])->name("report.money_out.subcategory.store");
        Route::put("/subcategory/{id}", [
            ReportMoneyOutController::class,
            "subcategoryUpdate",
        ])->name("report.money_out.subcategory.update");
        Route::delete("/subcategory/{id}", [
            ReportMoneyOutController::class,
            "subcategoryDestroy",
        ])->name("report.money_out.subcategory.destroy");
    });

    Route::get("reports", [ReportController::class, "index"])->name("reports");

    Route::get("payments", [PaymentController::class, "index"])->name(
        "payments"
    );
    Route::post("payment-setting-save", [
        PaymentController::class,
        "saveSk",
    ])->name("payment.save");

    // User Subscription Routes
    Route::prefix("subscriptions")->group(function () {
        // Original subscription routes
        Route::get("/", [SubscriptionController::class, "index"])->name(
            "user.subscriptions.index"
        );
        // Route::get('/{id}', [SubscriptionController::class, 'show'])->name('user.subscriptions.show');
        Route::post("/{planId}/subscribe", [
            SubscriptionController::class,
            "subscribe",
        ])->name("user.subscriptions.subscribe");
        Route::get("/payment/{subscriptionId}", [
            SubscriptionController::class,
            "showPayment",
        ])->name("user.subscriptions.payment")->middleware('bayarcash.config');
        Route::post("/payment/{subscriptionId}", [
            SubscriptionController::class,
            "processPayment",
        ])->name("user.subscriptions.process-payment")->middleware('bayarcash.config');
        Route::get("/thankyou/{subscriptionId}", [
            SubscriptionController::class,
            "thankYou",
        ])->name("user.subscriptions.thankyou");
        // Route::post('/cancel', [SubscriptionController::class, 'cancel'])->name('user.subscriptions.cancel');
        // Route::post('/resume', [SubscriptionController::class, 'resume'])->name('user.subscriptions.resume');
    });

    // New Subscription Management Routes
    Route::prefix("subscription-management")->group(function () {
        Route::get("/", [
            SubscriptionManagementController::class,
            "index",
        ])->name("user.subscriptions.management");
        Route::get("/subscription/{id}", [
            SubscriptionManagementController::class,
            "show",
        ])->name("user.subscriptions.show");
        Route::get("/plan/{id}", [
            SubscriptionManagementController::class,
            "showPlan",
        ])->name("user.subscriptions.plan.show");
        Route::get("/cancel", [
            SubscriptionManagementController::class,
            "cancelForm",
        ])->name("user.subscriptions.cancel");
        Route::post("/cancel", [
            SubscriptionManagementController::class,
            "processCancel",
        ])->name("user.subscriptions.cancel.process");
        Route::get("/resume/{id}", [
            SubscriptionManagementController::class,
            "resume",
        ])->name("user.subscriptions.resume");
        Route::get("/upgrade/{planId}", [
            SubscriptionManagementController::class,
            "upgradeForm",
        ])->name("user.subscriptions.upgrade.form")->middleware('bayarcash.config');
        Route::post("/upgrade/{planId}", [
            SubscriptionManagementController::class,
            "processUpgrade",
        ])->name("user.subscriptions.upgrade.process")->middleware('bayarcash.config');
    });

    // Addon Management Routes
    Route::prefix("addons")->group(function () {
        Route::get("/", [AddonManagementController::class, "index"])->name(
            "user.addons.index"
        );
        Route::get("/limits", [
            AddonManagementController::class,
            "limits",
        ])->name("user.addons.limits");
        Route::get("/{id}", [AddonManagementController::class, "show"])->name(
            "user.addons.show"
        );
        Route::get("/{id}/purchase", [
            AddonManagementController::class,
            "purchaseForm",
        ])->name("user.addons.purchase.form");
        Route::post("/{id}/purchase", [
            AddonManagementController::class,
            "processPurchase",
        ])->name("user.addons.purchase.process");
        Route::get("/thankyou/{id}", [
            AddonManagementController::class,
            "thankYou",
        ])->name("user.addons.thankyou");
        Route::get("/{id}/cancel", [
            AddonManagementController::class,
            "cancelForm",
        ])->name("user.addons.cancel");
        Route::post("/{id}/cancel", [
            AddonManagementController::class,
            "processCancel",
        ])->name("user.addons.cancel.process");

        // Limit Upgrade Routes
        Route::get("/limit-upgrade/{id}/purchase", [
            AddonManagementController::class,
            "limitUpgradePurchaseForm",
        ])->name("user.addons.limit-upgrade.purchase.form");
        Route::post("/limit-upgrade/{id}/purchase", [
            AddonManagementController::class,
            "processLimitUpgradePurchase",
        ])->name("user.addons.limit-upgrade.purchase.process");
    });
});

// Add this to routes/web.php
Route::get("/refresh-csrf", function () {
    return response()
        ->json([
            "token" => csrf_token(),
        ])
        ->cookie("XSRF-TOKEN", csrf_token(), config("session.lifetime"));
})->middleware("web");

// SSO
Route::match(["get", "post"], "/v3/sso/login", [
    V3AuthController::class,
    "ssoLogin",
])
    ->name("sso.login")
    ->middleware("web");
Route::match(["get", "post"], "/v3/sso/mobile-login", [
    V3AuthController::class,
    "mobileSsoLogin",
])->name("sso.mobile.login");
Route::get("/v3/sso/csrf-refresh", function (Request $request) {
    return response()
        ->json([
            "token" => csrf_token(),
        ])
        ->cookie(
            "XSRF-TOKEN",
            csrf_token(),
            config("session.lifetime"),
            null,
            null,
            $request->secure(),
            false // Not HttpOnly to allow JavaScript access
        );
})
    ->middleware("web")
    ->name("sso.csrf-refresh");

Route::get("/token/refresh", function (Request $request) {
    $token = $request->token;
    $redirectUrl = $request->redirect;

    // Set token in cookie or session
    return redirect($redirectUrl)->withCookie(
        cookie("auth_token", $token, 60 * 24 * 30, null, null, false, true)
    );
})->name("token.refresh");

// Console routes for log viewing
Route::prefix("console")
    ->name("console.")
    ->group(function () {
        Route::get("/", [ConsoleController::class, "index"])->name("index");
        Route::post("/auth", [ConsoleController::class, "authenticate"])->name(
            "auth"
        );
        Route::get("/logs", [ConsoleController::class, "logs"])
            ->name("logs")
            ->middleware("console.auth");
        Route::get("/view-log", [ConsoleController::class, "viewLog"])
            ->name("view-log")
            ->middleware("console.auth");
        Route::get("/download-log", [ConsoleController::class, "downloadLog"])
            ->name("download-log")
            ->middleware("console.auth");
        // Fix the logout route to use the controller class reference
        Route::post("/logout", [ConsoleController::class, "logout"])->name(
            "logout"
        );
        Route::get("/phpinfo", [ConsoleController::class, "phpinfo"])
            ->name("phpinfo")
            ->middleware("console.auth");
    });

// Test route for emoji validation
Route::get("/test-emoji-backend", function () {
    $testCases = [
        "Hello 😀 World" => "should fail",
        "Hello World" => "should pass",
        "Company 🏢 Name" => "should fail",
        "Company Name Ltd" => "should pass",
        "Footer text 🎉" => "should fail",
        "Footer text" => "should pass",
    ];

    $results = [];
    foreach ($testCases as $text => $expected) {
        $validator = \Illuminate\Support\Facades\Validator::make(
            ["test" => $text],
            ["test" => new \App\Rules\NoEmojis()]
        );
        $results[] = [
            "text" => $text,
            "expected" => $expected,
            "fails" => $validator->fails(),
            "error" => $validator->errors()->first("test"),
        ];
    }

    return response()->json(
        $results,
        200,
        [],
        JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE
    );
});

// Test route for welcome email
Route::get("/test-welcome-email", function () {
    // local
    $user = User::find("202aaac5-c840-4dab-abe1-c04d606f3683");
    $userDetail = $user->userDetails();
    $company = Company::Where(
        "user_id",
        "202aaac5-c840-4dab-abe1-c04d606f3683"
    )->first();
    $subscription = Subscription::find("dbe9ed79-0824-4537-9bab-16dfc3e4e764");

    //prod
    // $user = User::find('3f2fcec0-4583-4a01-8d52-fb970db87c95');
    // $userDetail = $user->userDetails();
    // $company = Company::Where('user_id','3f2fcec0-4583-4a01-8d52-fb970db87c95')->first();
    // $subscription = Subscription::find('ab44b08c-d830-4452-b3ee-84dff349b175');

    // Send welcome email
    \Illuminate\Support\Facades\Mail::to("<EMAIL>")->send(
        new \App\Mail\TrialWelcomeMail($user, $company)
    );

    return "Welcome email <NAME_EMAIL>";
});

// Internal Auth Routes
Route::prefix("internal")->group(function () {
    Route::get("/login", [InternalAuthController::class, "showLoginForm"])->name("internal.login");
    Route::post("/login", [InternalAuthController::class, "login"]);
    Route::post("/logout", [InternalAuthController::class, "logout"])->name("internal.logout");

    // Protected Internal Routes
    Route::middleware(["auth:internal"])->group(function () {
        Route::get("/dashboard", [InternalDashboardController::class, "index"])->name("internal.dashboard");
        Route::get("/users/export", [InternalDashboardController::class, "exportToExcel"])->name("internal.users.export");
    });
});

