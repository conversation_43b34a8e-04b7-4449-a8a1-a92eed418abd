<?php

namespace App\Http\Controllers\User;

use App\Models\Feature;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Services\SubscriptionEMandateService;
use App\Services\SubscriptionUpgradeLogger;

class SubscriptionManagementController extends Controller
{
    /**
     * Display a listing of the user's subscriptions.
     */
    public function index()
    {
        $user = auth()->user();

        if ($user->isBizappUser === 'Y' && !$user->companies) {
            return redirect()->back()->with('error', 'Please renew on Bizapp platform.');
        }

        // Get current active subscription
        $activeSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->with('subscriptionPlan')
            ->first();

        // Get subscription history
        $subscriptionHistory = Subscription::where('company_id', $user->companies->id)
            ->where(function($query) use ($activeSubscription) {
                if ($activeSubscription) {
                    $query->where('id', '!=', $activeSubscription->id);
                }
            })
            ->with('subscriptionPlan')
            ->orderBy('created_at', 'desc')
            ->get();
        // Get all available plans bar legacy plan
        $plans = SubscriptionPlan::where('is_active', true)
            ->where('name', '!=', 'Legacy Unlimited')
            ->orderBy('price')
            ->get();

        // Get all active features for comparison, only including those with codes starting with AO- or CF-
        $allFeatures = Feature::where('is_active', true)
            ->where(function($query) {
                $query->where('code', 'like', 'AO-%')
                      ->orWhere('code', 'like', 'CF-%');
            })
            ->orderBy('name')
            ->get();

        return view('user.subscriptions.index', compact(
            'activeSubscription',
            'subscriptionHistory',
            'plans',
            'allFeatures'
        ));
    }

    /**
     * Display the details of a specific subscription.
     */
    public function show($id)
    {
        $user = auth()->user();

        $subscription = Subscription::where('id', $id)
            ->where('company_id', $user->companies->id)
            ->with('subscriptionPlan.features')
            ->firstOrFail();

        // Get the plan from the subscription to match the view's expectations
        $plan = $subscription->subscriptionPlan;

        return view('user.subscriptions.show', compact('subscription', 'plan'));
    }

    /**
     * Display the details of a specific plan.
     */
    public function showPlan($id)
    {
        $user = auth()->user();

        // Get the plan details
        $plan = SubscriptionPlan::with('features')->findOrFail($id);

        return view('user.subscriptions.show', compact('plan'));
    }

    /**
     * Show the form for upgrading to a new subscription plan.
     */
    public function upgradeForm($planId)
    {
        $user = auth()->user();

        if (!$user->companies) {
            return redirect()->route('company.wizard')
                ->with('error', 'Please complete your company profile first.');
        }

        // Get the plan to upgrade to
        $plan = SubscriptionPlan::findOrFail($planId);

        // Get current active subscription
        $currentSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->with('subscriptionPlan')
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'You do not have an active subscription to upgrade.');
        }

        // Check if the new plan is actually an upgrade
        if ($plan->price <= $currentSubscription->subscriptionPlan->price) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'The selected plan is not an upgrade from your current plan.');
        }

        return view('user.subscriptions.upgrade', compact('plan', 'currentSubscription'));
    }

    /**
     * Process the subscription upgrade using BayarCash E-Mandate.
     */
    public function processUpgrade(Request $request, $planId, SubscriptionEMandateService $emandateService)
    {
        $user = auth()->user();

        if (!$user->companies) {
            return redirect()->route('company.wizard')
                ->with('error', 'Please complete your company profile first.');
        }

        // Get the plan to upgrade to
        $plan = SubscriptionPlan::findOrFail($planId);

        // Get current active subscription
        $currentSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'You do not have an active subscription to upgrade.');
        }

        // Check if the new plan is actually an upgrade
        if ($plan->price <= $currentSubscription->subscriptionPlan->price) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'The selected plan is not an upgrade from your current plan.');
        }

        // Calculate upgrade amount (pro-rated if needed)
        $upgradeAmount = $this->calculateUpgradeAmount($currentSubscription, $plan);

        // Log upgrade initiation
        SubscriptionUpgradeLogger::logUpgradeInitiation(
            $user,
            $user->companies,
            $currentSubscription,
            $plan,
            $upgradeAmount
        );

        DB::beginTransaction();
        try {
            // Log upgrade amount calculation details
            SubscriptionUpgradeLogger::logUpgradeAmountCalculation(
                $currentSubscription,
                $plan,
                $upgradeAmount,
                [
                    'current_subscription_days_remaining' => $currentSubscription->ends_at ?
                        now()->diffInDays($currentSubscription->ends_at) : null,
                    'current_subscription_total_days' => $currentSubscription->ends_at ?
                        $currentSubscription->starts_at->diffInDays($currentSubscription->ends_at) : null
                ]
            );

            // Cancel the current subscription (but let it run until the end of its term)
            $currentSubscription->update([
                'cancels_at' => $currentSubscription->ends_at,
                'canceled_at' => now()
            ]);

            Log::channel('subscription_upgrade_emandate')->info('Current subscription marked for cancellation', [
                'event' => 'current_subscription_cancelled',
                'subscription_id' => $currentSubscription->id,
                'cancels_at' => $currentSubscription->ends_at?->toISOString(),
                'canceled_at' => now()->toISOString()
            ]);

            // Calculate subscription dates for the new subscription
            $startsAt = now();
            $endsAt = null;

            if ($plan->duration_in_seconds > 0) {
                $endsAt = $startsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Calculate trial period if applicable
            $trialEndsAt = null;
            if ($plan->trial_period_days > 0) {
                $trialEndsAt = $startsAt->copy()->addDays($plan->trial_period_days);
                // If there's a trial, the subscription starts after the trial
                $endsAt = $trialEndsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Create new subscription with pending status (will be activated after e-mandate setup)
            $newSubscription = Subscription::create([
                'company_id' => $user->companies->id,
                'subscription_plan_id' => $plan->id,
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $trialEndsAt,
                'status' => 'pending', // Will be activated after e-mandate enrollment
                'upgrade_amount' => $upgradeAmount,
                'is_upgrade' => true,
                'previous_subscription_id' => $currentSubscription->id
            ]);

            // Log new subscription creation
            SubscriptionUpgradeLogger::logNewSubscriptionCreated($newSubscription);

            DB::commit();

            // Check if user has complete e-mandate data before proceeding
            $validationService = app(\App\Services\EMandateDataValidationService::class);
            $dataCheck = $validationService->canProceedWithEMandate($user);

            if (!$dataCheck['can_proceed']) {
                Log::channel('subscription_upgrade_emandate')->info('User data incomplete for e-mandate upgrade, redirecting to data collection', [
                    'subscription_id' => $newSubscription->id,
                    'user_id' => $user->id,
                    'validation_results' => $dataCheck['validation_results']
                ]);

                return redirect()->route('user.emandate.data-collection', [
                    'subscription_id' => $newSubscription->id,
                    'return_url' => route('user.subscriptions.upgrade.process', $plan->id)
                ])->with('info', 'Please complete your profile information to proceed with subscription upgrade.');
            }

            // Process BayarCash E-Mandate enrollment for subscription upgrade
            Log::channel('subscription_upgrade_emandate')->info('Starting e-mandate enrollment process', [
                'event' => 'emandate_process_start',
                'subscription_id' => $newSubscription->id,
                'plan_id' => $plan->id,
                'upgrade_amount' => $upgradeAmount,
                'user_id' => $user->id,
                'company_id' => $user->companies->id
            ]);

            // Create e-mandate enrollment for the upgrade
            $emandateResult = $emandateService->createSubscriptionEMandate($newSubscription);

            if ($emandateResult['success']) {
                if ($emandateResult['is_existing']) {
                    // Existing e-mandate found and linked
                    Log::channel('subscription_upgrade_emandate')->info('Existing e-mandate found and linked', [
                        'event' => 'existing_emandate_linked',
                        'subscription_id' => $newSubscription->id,
                        'enrollment_id' => $emandateResult['enrollment']->id,
                        'enrollment_status' => $emandateResult['enrollment']->status
                    ]);

                    // Activate the subscription immediately since e-mandate already exists
                    $newSubscription->update(['status' => 'active']);

                    SubscriptionUpgradeLogger::logUpgradeCompletion(
                        $newSubscription,
                        'success',
                        'Upgrade completed using existing e-mandate enrollment'
                    );

                    return redirect()->route('user.subscriptions.thankyou', $newSubscription->id)
                        ->with('success', 'Your subscription has been upgraded successfully using your existing e-mandate enrollment.');
                } else {
                    // New e-mandate enrollment created, redirect to BayarCash
                    Log::channel('subscription_upgrade_emandate')->info('Redirecting to BayarCash for new e-mandate enrollment', [
                        'event' => 'redirect_to_bayarcash',
                        'subscription_id' => $newSubscription->id,
                        'enrollment_id' => $emandateResult['enrollment']->id ?? null,
                        'enrollment_url' => $emandateResult['redirect_url']
                    ]);

                    return redirect($emandateResult['redirect_url']);
                }
            } else {
                // E-mandate enrollment failed
                Log::channel('subscription_upgrade_emandate')->error('E-mandate enrollment failed', [
                    'event' => 'emandate_enrollment_failed',
                    'subscription_id' => $newSubscription->id,
                    'error_message' => $emandateResult['message'],
                    'rollback_initiated' => true
                ]);

                // Rollback the subscription creation
                SubscriptionUpgradeLogger::logUpgradeRollback(
                    $newSubscription,
                    $currentSubscription,
                    'E-mandate enrollment failed: ' . $emandateResult['message']
                );

                $newSubscription->delete();
                $currentSubscription->update([
                    'cancels_at' => null,
                    'canceled_at' => null
                ]);

                return redirect()->route('user.subscriptions.management')
                    ->with('error', 'Failed to set up e-mandate enrollment for your subscription upgrade: ' . $emandateResult['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            SubscriptionUpgradeLogger::logError(
                'subscription_upgrade_process',
                $e,
                [
                    'user_id' => $user->id,
                    'company_id' => $user->companies->id,
                    'plan_id' => $planId,
                    'current_subscription_id' => $currentSubscription->id,
                    'upgrade_amount' => $upgradeAmount ?? null
                ]
            );

            return redirect()->route('user.subscriptions.index')
                ->with('error', 'Failed to upgrade subscription. Please try again later.');
        }
    }

    /**
     * Show the cancellation confirmation page.
     */
    public function cancelForm()
    {
        $user = auth()->user();

        $subscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->with('subscriptionPlan')
            ->firstOrFail();

        return view('user.subscriptions.cancel', compact('subscription'));
    }

    /**
     * Process the subscription cancellation.
     */
    public function processCancel(Request $request)
    {
        $user = auth()->user();

        $subscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->firstOrFail();

        $cancelImmediately = $request->has('cancel_immediately') && $request->cancel_immediately === 'yes';

        if ($cancelImmediately) {
            // Cancel immediately
            $subscription->update([
                'ends_at' => now(),
                'cancels_at' => now(),
                'canceled_at' => now(),
                'status' => 'canceled'
            ]);

            return redirect()->route('user.subscriptions.management')
                ->with('success', 'Your subscription has been canceled immediately.');
        } else {
            // Cancel at the end of the billing period
            $subscription->update([
                'cancels_at' => $subscription->ends_at,
                'canceled_at' => now()
            ]);

            return redirect()->route('user.subscriptions.management')
                ->with('success', 'Your subscription will be canceled at the end of the current billing period.');
        }
    }

    /**
     * Resume a canceled subscription (if it hasn't ended yet).
     */
    public function resume($id)
    {
        $user = auth()->user();

        $subscription = Subscription::where('id', $id)
            ->where('company_id', $user->companies->id)
            ->whereNotNull('canceled_at')
            ->where('status', '!=', 'canceled') // Not already fully canceled
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->firstOrFail();

        $subscription->update([
            'cancels_at' => null,
            'canceled_at' => null
        ]);

        return redirect()->route('user.subscriptions.management')
            ->with('success', 'Your subscription has been resumed.');
    }

    /**
     * Calculate the upgrade amount with pro-rating logic
     *
     * @param Subscription $currentSubscription
     * @param SubscriptionPlan $newPlan
     * @return float
     */
    private function calculateUpgradeAmount(Subscription $currentSubscription, SubscriptionPlan $newPlan): float
    {
        $currentPlan = $currentSubscription->subscriptionPlan;

        // Basic upgrade amount (difference between plans)
        $upgradeAmount = $newPlan->price - $currentPlan->price;

        // If current subscription has an end date, calculate pro-rated amount
        if ($currentSubscription->ends_at) {
            $totalDays = $currentSubscription->starts_at->diffInDays($currentSubscription->ends_at);
            $remainingDays = now()->diffInDays($currentSubscription->ends_at);

            if ($totalDays > 0 && $remainingDays > 0) {
                // Pro-rate the upgrade amount based on remaining subscription time
                $proRateMultiplier = $remainingDays / $totalDays;
                $upgradeAmount = $upgradeAmount * $proRateMultiplier;
            }
        }

        // Ensure minimum upgrade amount
        return max($upgradeAmount, 0);
    }
}
